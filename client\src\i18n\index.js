import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Translation resources
const resources = {
  en: {
    translation: {
      // Navigation
      home: 'Home',
      menu: 'Menu',
      orders: 'My Orders',
      specialOrders: 'Special Orders',
      profile: 'Profile',
      login: 'Login',
      register: 'Register',
      logout: 'Logout',
      
      // Common
      welcome: 'Welcome to Zina Chop House',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      edit: 'Edit',
      delete: 'Delete',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      search: 'Search',
      filter: 'Filter',
      
      // Menu
      menuTitle: 'Our Menu',
      menuDescription: 'Authentic Cameroonian cuisine made with love',
      addToCart: 'Add to Cart',
      viewDetails: 'View Details',
      price: 'Price',
      availability: 'Availability',
      available: 'Available',
      unavailable: 'Unavailable',
      ingredients: 'Ingredients',
      allergens: 'Allergens',
      
      // Weekly Menu
      weeklyMenu: 'Weekly Menu',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday',
      njamaFriday: 'Njama Njama Friday',
      
      // Cart & Orders
      cart: 'Cart',
      checkout: 'Checkout',
      orderNow: 'Order Now',
      orderSummary: 'Order Summary',
      orderHistory: 'Order History',
      orderStatus: 'Order Status',
      orderNumber: 'Order Number',
      orderTotal: 'Total',
      deliveryInfo: 'Delivery Information',
      paymentMethod: 'Payment Method',
      
      // Order Status
      pending: 'Pending',
      confirmed: 'Confirmed',
      preparing: 'Preparing',
      ready: 'Ready',
      outForDelivery: 'Out for Delivery',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
      
      // Special Orders
      specialOrdersTitle: 'Special Orders',
      eventOrders: 'Event Orders',
      bulkOrders: 'Bulk Orders',
      eventName: 'Event Name',
      eventDate: 'Event Date',
      eventTime: 'Event Time',
      numberOfPeople: 'Number of People',
      mealType: 'Meal Type',
      servings: 'Servings',
      deliveryDay: 'Delivery Day',
      specialRequests: 'Special Requests',
      callToConfirm: 'Call me to confirm',
      
      // Contact & Delivery
      contactInfo: 'Contact Information',
      deliveryAddress: 'Delivery Address',
      phoneNumber: 'Phone Number',
      emailAddress: 'Email Address',
      delivery: 'Delivery',
      pickup: 'Pickup',
      deliveryInformation: 'Delivery Information',
      addressRequired: 'Address is required',
      addressTooShort: 'Address is too short',
      enterDeliveryAddress: 'Enter your delivery address',
      city: 'City',
      cityRequired: 'City is required',
      calculatingDelivery: 'Calculating delivery fee...',
      deliveryAvailable: 'Delivery Available',
      estimatedTime: 'Estimated Time',
      discount: 'Discount',
      savingsOpportunities: 'Savings Opportunities',
      save: 'Save',
      deliveryZones: 'Delivery Zones',
      deliveryType: 'Delivery Type',
      scheduledTime: 'Scheduled Time',
      preferredTime: 'Preferred Time',
      optional: 'Optional',
      deliveryInstructions: 'Delivery Instructions',
      deliveryInstructionsPlaceholder: 'Any special instructions for delivery...',
      subtotal: 'Subtotal',
      deliveryDiscount: 'Delivery Discount',
      estimatedDeliveryTime: 'Estimated Delivery Time',
      minutes: 'minutes',
      total: 'Total',
      placeOrder: 'Place Order',
      
      // Payment
      cashOnDelivery: 'Cash on Delivery',
      mtnMobileMoney: 'MTN Mobile Money',
      orangeMoney: 'Orange Money',
      payWithCashWhenOrderArrives: 'Pay with cash when your order arrives',
      payWithMTNMobileMoney: 'Pay with your MTN Mobile Money account',
      payWithOrangeMoney: 'Pay with your Orange Money account',
      processingTime: 'Processing Time',
      immediate: 'Immediate',
      instant: 'Instant',
      fees: 'Fees',
      phoneNumberFormat: 'Phone Number Format',
      phoneNumberRequired: 'Phone number is required',
      invalidPhoneFormat: 'Invalid phone number format',
      paymentSummary: 'Payment Summary',
      orderValue: 'Order Value',
      deliveryFee: 'Delivery Fee',
      free: 'Free',
      totalAmount: 'Total Amount',
      securePaymentNotice: 'Your payment information is secure and encrypted',
      mobileMoneyInfo: 'Mobile Money Information',
      mtnNumbers: 'MTN Numbers',
      orangeNumbers: 'Orange Numbers',
      ensureSufficientBalance: 'Ensure you have sufficient balance',
      transactionFeesApply: 'Transaction fees may apply',
      
      // Feedback
      feedback: 'Feedback',
      rating: 'Rating',
      review: 'Review',
      submitFeedback: 'Submit Feedback',
      wouldRecommend: 'Would you recommend us?',
      wouldOrderAgain: 'Would you order again?',
      
      // Authentication
      loginTitle: 'Login to Your Account',
      registerTitle: 'Create New Account',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      name: 'Full Name',
      forgotPassword: 'Forgot Password?',
      
      // Footer
      aboutUs: 'About Us',
      contactUs: 'Contact Us',
      privacyPolicy: 'Privacy Policy',
      termsOfService: 'Terms of Service',
      
      // Messages
      orderPlacedSuccess: 'Order placed successfully!',
      loginSuccess: 'Login successful!',
      registrationSuccess: 'Registration successful!',
      feedbackSubmitted: 'Thank you for your feedback!',
      specialOrderSubmitted: 'Special order submitted successfully!',

      // Language
      selectLanguage: 'Select Language',
      languageChangeNote: 'Language changes apply to the entire application'
    }
  },
  fr: {
    translation: {
      // Navigation
      home: 'Accueil',
      menu: 'Menu',
      orders: 'Mes Commandes',
      specialOrders: 'Commandes Spéciales',
      profile: 'Profil',
      login: 'Connexion',
      register: 'S\'inscrire',
      logout: 'Déconnexion',
      
      // Common
      welcome: 'Bienvenue chez Zina Chop House',
      loading: 'Chargement...',
      error: 'Erreur',
      success: 'Succès',
      cancel: 'Annuler',
      confirm: 'Confirmer',
      save: 'Enregistrer',
      edit: 'Modifier',
      delete: 'Supprimer',
      back: 'Retour',
      next: 'Suivant',
      previous: 'Précédent',
      search: 'Rechercher',
      filter: 'Filtrer',
      
      // Menu
      menuTitle: 'Notre Menu',
      menuDescription: 'Cuisine camerounaise authentique préparée avec amour',
      addToCart: 'Ajouter au Panier',
      viewDetails: 'Voir Détails',
      price: 'Prix',
      availability: 'Disponibilité',
      available: 'Disponible',
      unavailable: 'Indisponible',
      ingredients: 'Ingrédients',
      allergens: 'Allergènes',
      
      // Weekly Menu
      weeklyMenu: 'Menu Hebdomadaire',
      monday: 'Lundi',
      tuesday: 'Mardi',
      wednesday: 'Mercredi',
      thursday: 'Jeudi',
      friday: 'Vendredi',
      saturday: 'Samedi',
      sunday: 'Dimanche',
      njamaFriday: 'Vendredi Njama Njama',
      
      // Cart & Orders
      cart: 'Panier',
      checkout: 'Commander',
      orderNow: 'Commander Maintenant',
      orderSummary: 'Résumé de Commande',
      orderHistory: 'Historique des Commandes',
      orderStatus: 'Statut de Commande',
      orderNumber: 'Numéro de Commande',
      orderTotal: 'Total',
      deliveryInfo: 'Informations de Livraison',
      paymentMethod: 'Méthode de Paiement',
      
      // Order Status
      pending: 'En Attente',
      confirmed: 'Confirmée',
      preparing: 'En Préparation',
      ready: 'Prête',
      outForDelivery: 'En Livraison',
      delivered: 'Livrée',
      cancelled: 'Annulée',
      
      // Special Orders
      specialOrdersTitle: 'Commandes Spéciales',
      eventOrders: 'Commandes d\'Événement',
      bulkOrders: 'Commandes en Gros',
      eventName: 'Nom de l\'Événement',
      eventDate: 'Date de l\'Événement',
      eventTime: 'Heure de l\'Événement',
      numberOfPeople: 'Nombre de Personnes',
      mealType: 'Type de Repas',
      servings: 'Portions',
      deliveryDay: 'Jour de Livraison',
      specialRequests: 'Demandes Spéciales',
      callToConfirm: 'Appelez-moi pour confirmer',
      
      // Contact & Delivery
      contactInfo: 'Informations de Contact',
      deliveryAddress: 'Adresse de Livraison',
      phoneNumber: 'Numéro de Téléphone',
      emailAddress: 'Adresse Email',
      delivery: 'Livraison',
      pickup: 'Retrait',
      deliveryInformation: 'Informations de Livraison',
      addressRequired: 'L\'adresse est requise',
      addressTooShort: 'L\'adresse est trop courte',
      enterDeliveryAddress: 'Entrez votre adresse de livraison',
      city: 'Ville',
      cityRequired: 'La ville est requise',
      calculatingDelivery: 'Calcul des frais de livraison...',
      deliveryAvailable: 'Livraison Disponible',
      estimatedTime: 'Temps Estimé',
      discount: 'Remise',
      savingsOpportunities: 'Opportunités d\'Économies',
      save: 'Économiser',
      deliveryZones: 'Zones de Livraison',
      deliveryType: 'Type de Livraison',
      scheduledTime: 'Heure Programmée',
      preferredTime: 'Heure Préférée',
      optional: 'Optionnel',
      deliveryInstructions: 'Instructions de Livraison',
      deliveryInstructionsPlaceholder: 'Instructions spéciales pour la livraison...',
      subtotal: 'Sous-total',
      deliveryDiscount: 'Remise de Livraison',
      estimatedDeliveryTime: 'Temps de Livraison Estimé',
      minutes: 'minutes',
      total: 'Total',
      placeOrder: 'Passer Commande',
      
      // Payment
      cashOnDelivery: 'Paiement à la Livraison',
      mtnMobileMoney: 'MTN Mobile Money',
      orangeMoney: 'Orange Money',
      payWithCashWhenOrderArrives: 'Payez en espèces à la livraison de votre commande',
      payWithMTNMobileMoney: 'Payez avec votre compte MTN Mobile Money',
      payWithOrangeMoney: 'Payez avec votre compte Orange Money',
      processingTime: 'Temps de Traitement',
      immediate: 'Immédiat',
      instant: 'Instantané',
      fees: 'Frais',
      phoneNumberFormat: 'Format du Numéro de Téléphone',
      phoneNumberRequired: 'Le numéro de téléphone est requis',
      invalidPhoneFormat: 'Format de numéro de téléphone invalide',
      paymentSummary: 'Résumé du Paiement',
      orderValue: 'Valeur de la Commande',
      deliveryFee: 'Frais de Livraison',
      free: 'Gratuit',
      totalAmount: 'Montant Total',
      securePaymentNotice: 'Vos informations de paiement sont sécurisées et cryptées',
      mobileMoneyInfo: 'Informations Mobile Money',
      mtnNumbers: 'Numéros MTN',
      orangeNumbers: 'Numéros Orange',
      ensureSufficientBalance: 'Assurez-vous d\'avoir un solde suffisant',
      transactionFeesApply: 'Des frais de transaction peuvent s\'appliquer',
      
      // Feedback
      feedback: 'Commentaires',
      rating: 'Note',
      review: 'Avis',
      submitFeedback: 'Soumettre Commentaires',
      wouldRecommend: 'Nous recommanderiez-vous?',
      wouldOrderAgain: 'Commanderiez-vous à nouveau?',
      
      // Authentication
      loginTitle: 'Connectez-vous à Votre Compte',
      registerTitle: 'Créer un Nouveau Compte',
      email: 'Email',
      password: 'Mot de Passe',
      confirmPassword: 'Confirmer Mot de Passe',
      name: 'Nom Complet',
      forgotPassword: 'Mot de passe oublié?',
      
      // Footer
      aboutUs: 'À Propos',
      contactUs: 'Contactez-nous',
      privacyPolicy: 'Politique de Confidentialité',
      termsOfService: 'Conditions d\'Utilisation',
      
      // Messages
      orderPlacedSuccess: 'Commande passée avec succès!',
      loginSuccess: 'Connexion réussie!',
      registrationSuccess: 'Inscription réussie!',
      feedbackSubmitted: 'Merci pour vos commentaires!',
      specialOrderSubmitted: 'Commande spéciale soumise avec succès!',

      // Language
      selectLanguage: 'Sélectionner la Langue',
      languageChangeNote: 'Les changements de langue s\'appliquent à toute l\'application'
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage']
    }
  });

export default i18n;

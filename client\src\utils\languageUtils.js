/**
 * Language utility functions for managing language preferences
 */

// Supported languages
export const SUPPORTED_LANGUAGES = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false
  }
];

// Default language
export const DEFAULT_LANGUAGE = 'en';

// Local storage key for language preference
const LANGUAGE_STORAGE_KEY = 'zina-chop-house-language';

/**
 * Get the user's preferred language from localStorage
 * @returns {string} Language code
 */
export const getStoredLanguage = () => {
  try {
    const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (stored && SUPPORTED_LANGUAGES.some(lang => lang.code === stored)) {
      return stored;
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error);
  }
  return null;
};

/**
 * Store the user's language preference
 * @param {string} languageCode - Language code to store
 */
export const storeLanguage = (languageCode) => {
  try {
    if (SUPPORTED_LANGUAGES.some(lang => lang.code === languageCode)) {
      localStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
      
      // Update document attributes
      document.documentElement.lang = languageCode;
      
      // Update document direction if needed (for future RTL support)
      const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
      if (language) {
        document.documentElement.dir = language.rtl ? 'rtl' : 'ltr';
      }
      
      return true;
    }
  } catch (error) {
    console.warn('Failed to store language:', error);
  }
  return false;
};

/**
 * Detect the user's preferred language from browser settings
 * @returns {string} Language code
 */
export const detectBrowserLanguage = () => {
  try {
    // Check navigator.language first
    const browserLang = navigator.language || navigator.userLanguage;
    if (browserLang) {
      // Extract language code (e.g., 'en-US' -> 'en')
      const langCode = browserLang.split('-')[0].toLowerCase();
      if (SUPPORTED_LANGUAGES.some(lang => lang.code === langCode)) {
        return langCode;
      }
    }
    
    // Check navigator.languages array
    if (navigator.languages && navigator.languages.length > 0) {
      for (const lang of navigator.languages) {
        const langCode = lang.split('-')[0].toLowerCase();
        if (SUPPORTED_LANGUAGES.some(supportedLang => supportedLang.code === langCode)) {
          return langCode;
        }
      }
    }
  } catch (error) {
    console.warn('Failed to detect browser language:', error);
  }
  
  return DEFAULT_LANGUAGE;
};

/**
 * Get the best language to use based on stored preference, browser detection, and fallback
 * @returns {string} Language code
 */
export const getBestLanguage = () => {
  // 1. Check stored preference
  const stored = getStoredLanguage();
  if (stored) {
    return stored;
  }
  
  // 2. Detect from browser
  const detected = detectBrowserLanguage();
  if (detected !== DEFAULT_LANGUAGE) {
    return detected;
  }
  
  // 3. Fallback to default
  return DEFAULT_LANGUAGE;
};

/**
 * Get language information by code
 * @param {string} languageCode - Language code
 * @returns {object|null} Language information object
 */
export const getLanguageInfo = (languageCode) => {
  return SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode) || null;
};

/**
 * Check if a language is supported
 * @param {string} languageCode - Language code to check
 * @returns {boolean} Whether the language is supported
 */
export const isLanguageSupported = (languageCode) => {
  return SUPPORTED_LANGUAGES.some(lang => lang.code === languageCode);
};

/**
 * Format a language name for display
 * @param {string} languageCode - Language code
 * @param {boolean} useNativeName - Whether to use native name
 * @returns {string} Formatted language name
 */
export const formatLanguageName = (languageCode, useNativeName = true) => {
  const language = getLanguageInfo(languageCode);
  if (!language) {
    return languageCode.toUpperCase();
  }
  
  return useNativeName ? language.nativeName : language.name;
};

/**
 * Get language flag emoji
 * @param {string} languageCode - Language code
 * @returns {string} Flag emoji
 */
export const getLanguageFlag = (languageCode) => {
  const language = getLanguageInfo(languageCode);
  return language ? language.flag : '🌐';
};

/**
 * Initialize language settings on app startup
 * @param {object} i18n - i18next instance
 */
export const initializeLanguage = (i18n) => {
  const bestLanguage = getBestLanguage();
  
  // Set the language in i18next
  i18n.changeLanguage(bestLanguage);
  
  // Store the language preference
  storeLanguage(bestLanguage);
  
  // Set up language change listener to persist changes
  i18n.on('languageChanged', (lng) => {
    storeLanguage(lng);
  });
  
  console.log(`Language initialized: ${bestLanguage}`);
};

/**
 * Switch to a specific language
 * @param {object} i18n - i18next instance
 * @param {string} languageCode - Language code to switch to
 * @returns {Promise<boolean>} Success status
 */
export const switchLanguage = async (i18n, languageCode) => {
  try {
    if (!isLanguageSupported(languageCode)) {
      console.warn(`Unsupported language: ${languageCode}`);
      return false;
    }
    
    await i18n.changeLanguage(languageCode);
    storeLanguage(languageCode);
    
    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('languageChanged', {
      detail: { language: languageCode }
    }));
    
    return true;
  } catch (error) {
    console.error('Failed to switch language:', error);
    return false;
  }
};

/**
 * Get localized date format
 * @param {string} languageCode - Language code
 * @returns {object} Date format options
 */
export const getDateFormat = (languageCode) => {
  const formats = {
    en: {
      short: { month: 'short', day: 'numeric', year: 'numeric' },
      long: { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' },
      time: { hour: '2-digit', minute: '2-digit' }
    },
    fr: {
      short: { day: 'numeric', month: 'short', year: 'numeric' },
      long: { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' },
      time: { hour: '2-digit', minute: '2-digit' }
    }
  };
  
  return formats[languageCode] || formats[DEFAULT_LANGUAGE];
};

/**
 * Format a date according to the current language
 * @param {Date|string} date - Date to format
 * @param {string} languageCode - Language code
 * @param {string} format - Format type ('short', 'long', 'time')
 * @returns {string} Formatted date
 */
export const formatDate = (date, languageCode, format = 'short') => {
  try {
    const dateObj = date instanceof Date ? date : new Date(date);
    const formatOptions = getDateFormat(languageCode)[format];
    
    return dateObj.toLocaleDateString(languageCode, formatOptions);
  } catch (error) {
    console.warn('Failed to format date:', error);
    return date.toString();
  }
};

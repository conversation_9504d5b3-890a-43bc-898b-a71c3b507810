import mongoose from 'mongoose';

const paymentSchema = new mongoose.Schema({
  orderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'XAF',
    enum: ['XAF', 'EUR', 'USD']
  },
  method: {
    type: String,
    required: true,
    enum: ['mtn-mobile-money', 'orange-money', 'cash-on-delivery', 'bank-transfer']
  },
  provider: {
    type: String,
    required: true,
    enum: ['MTN', 'Orange', 'COD', 'Bank']
  },
  transactionRef: {
    type: String,
    required: true,
    unique: true
  },
  providerTransactionId: {
    type: String,
    sparse: true
  },
  phoneNumber: {
    type: String,
    validate: {
      validator: function(v) {
        // Cameroon phone number validation
        return /^237(6[5-9]|2[2-3])\d{7}$/.test(v.replace(/\s+/g, ''));
      },
      message: 'Invalid Cameroon phone number format'
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  paymentDate: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  failedAt: {
    type: Date
  },
  errorMessage: {
    type: String
  },
  providerResponse: {
    type: mongoose.Schema.Types.Mixed
  },
  metadata: {
    orderNumber: String,
    customerName: String,
    customerEmail: String,
    deliveryAddress: String,
    notes: String
  },
  fees: {
    processingFee: {
      type: Number,
      default: 0
    },
    providerFee: {
      type: Number,
      default: 0
    },
    totalFees: {
      type: Number,
      default: 0
    }
  },
  refund: {
    refunded: {
      type: Boolean,
      default: false
    },
    refundAmount: {
      type: Number,
      default: 0
    },
    refundDate: Date,
    refundReason: String,
    refundTransactionId: String
  }
}, {
  timestamps: true
});

// Indexes for better query performance
paymentSchema.index({ orderId: 1 });
paymentSchema.index({ userId: 1 });
paymentSchema.index({ transactionRef: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ method: 1 });
paymentSchema.index({ createdAt: -1 });

// Virtual for payment age
paymentSchema.virtual('paymentAge').get(function() {
  if (this.completedAt) {
    return this.completedAt - this.createdAt;
  }
  return Date.now() - this.createdAt;
});

// Pre-save middleware
paymentSchema.pre('save', function(next) {
  // Set completion date when status changes to completed
  if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
    this.paymentDate = new Date();
  }
  
  // Set failed date when status changes to failed
  if (this.isModified('status') && this.status === 'failed' && !this.failedAt) {
    this.failedAt = new Date();
  }
  
  // Calculate total fees
  this.fees.totalFees = (this.fees.processingFee || 0) + (this.fees.providerFee || 0);
  
  next();
});

// Static methods
paymentSchema.statics.findByTransactionRef = function(transactionRef) {
  return this.findOne({ transactionRef });
};

paymentSchema.statics.findByOrderId = function(orderId) {
  return this.find({ orderId }).sort({ createdAt: -1 });
};

paymentSchema.statics.getPaymentStats = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: {
          status: '$status',
          method: '$method'
        },
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        avgAmount: { $avg: '$amount' }
      }
    },
    {
      $sort: { '_id.method': 1, '_id.status': 1 }
    }
  ]);
};

// Instance methods
paymentSchema.methods.markAsCompleted = function(providerTransactionId) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.paymentDate = new Date();
  if (providerTransactionId) {
    this.providerTransactionId = providerTransactionId;
  }
  return this.save();
};

paymentSchema.methods.markAsFailed = function(errorMessage) {
  this.status = 'failed';
  this.failedAt = new Date();
  this.errorMessage = errorMessage;
  return this.save();
};

paymentSchema.methods.processRefund = function(refundAmount, reason) {
  this.refund.refunded = true;
  this.refund.refundAmount = refundAmount || this.amount;
  this.refund.refundDate = new Date();
  this.refund.refundReason = reason;
  this.status = 'refunded';
  return this.save();
};

// Transform output
paymentSchema.methods.toJSON = function() {
  const payment = this.toObject();
  
  // Remove sensitive information
  delete payment.providerResponse;
  
  // Format dates
  if (payment.createdAt) {
    payment.createdAt = payment.createdAt.toISOString();
  }
  if (payment.completedAt) {
    payment.completedAt = payment.completedAt.toISOString();
  }
  if (payment.paymentDate) {
    payment.paymentDate = payment.paymentDate.toISOString();
  }
  
  return payment;
};

const Payment = mongoose.model('Payment', paymentSchema);

export default Payment;

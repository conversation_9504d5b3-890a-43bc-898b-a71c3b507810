import mongoose from 'mongoose';

const orderItemSchema = new mongoose.Schema({
  menuItem: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MenuItem',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  specialInstructions: String
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [orderItemSchema],
  totalAmount: {
    type: Number,
    required: true,
    min: [0, 'Total amount cannot be negative']
  },
  status: {
    type: String,
    enum: [
      'pending',
      'confirmed',
      'preparing',
      'ready',
      'out-for-delivery',
      'delivered',
      'cancelled',
      'refunded'
    ],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash-on-delivery', 'mtn-mobile-money', 'orange-money'],
    required: true
  },
  paymentTiming: {
    type: String,
    enum: ['before-delivery', 'on-delivery'],
    default: 'on-delivery'
  },
  deliveryInfo: {
    type: {
      type: String,
      enum: ['delivery', 'pickup'],
      required: true
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: {
        type: String,
        default: 'Cameroon'
      },
      coordinates: {
        latitude: Number,
        longitude: Number
      },
      instructions: String
    },
    distance: {
      type: Number, // in kilometers
      default: 0
    },
    deliveryFee: {
      type: Number,
      default: 1000 // Base fee in XAF
    },
    deliveryDiscount: {
      type: Number,
      default: 0 // Discount percentage (0-50)
    },
    scheduledTime: Date,
    estimatedDeliveryTime: Date,
    actualDeliveryTime: Date
  },
  assignedDeliveryPartner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  customerNotes: String,
  internalNotes: String,
  statusHistory: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  estimatedPreparationTime: {
    type: Number, // in minutes
    default: 30
  },
  actualPreparationTime: Number,
  rating: {
    food: {
      type: Number,
      min: 1,
      max: 5
    },
    delivery: {
      type: Number,
      min: 1,
      max: 5
    },
    overall: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  feedback: String,
  isSpecialOrder: {
    type: Boolean,
    default: false
  },
  refundAmount: {
    type: Number,
    default: 0
  },
  refundReason: String
}, {
  timestamps: true
});

// Indexes for better query performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customer: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ assignedDeliveryPartner: 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'deliveryInfo.scheduledTime': 1 });

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.orderNumber = `ZCH${dateStr}${randomNum}`;
  }
  next();
});

// Method to add status update
orderSchema.methods.updateStatus = function(newStatus, updatedBy, notes = '') {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    updatedBy,
    notes
  });
  
  // Update timestamps based on status
  if (newStatus === 'delivered') {
    this.deliveryInfo.actualDeliveryTime = new Date();
  }
  
  return this.save();
};

// Method to calculate estimated delivery time
orderSchema.methods.calculateEstimatedDeliveryTime = function() {
  const now = new Date();
  const prepTime = this.estimatedPreparationTime || 30;
  const deliveryTime = this.deliveryInfo.type === 'delivery' ? 20 : 0; // 20 min for delivery
  
  this.deliveryInfo.estimatedDeliveryTime = new Date(now.getTime() + (prepTime + deliveryTime) * 60000);
  return this.save();
};

// Virtual for order age in minutes
orderSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60));
});

// Calculate delivery fee based on distance and order value
orderSchema.methods.calculateDeliveryFee = function() {
  const baseFee = 1000; // 1000 XAF base
  const distanceRate = 200; // 200 XAF per km after 5km
  const freeDeliveryThreshold = 10000; // Free delivery above 10k XAF
  
  let deliveryFee = baseFee;
  
  // Add distance-based fee (free for first 5km)
  if (this.deliveryInfo.distance > 5) {
    deliveryFee += (this.deliveryInfo.distance - 5) * distanceRate;
  }
  
  // Apply discounts based on order value
  let discount = 0;
  if (this.totalAmount >= 10000) {
    discount = 10; // 10% discount
  }
  if (this.totalAmount >= 20000) {
    discount = 25; // 25% discount
  }
  if (this.totalAmount >= 50000) {
    discount = 50; // 50% discount
  }
  
  // Apply discount
  deliveryFee = deliveryFee * (1 - discount / 100);
  
  // Free delivery for high-value orders
  if (this.totalAmount >= freeDeliveryThreshold && discount >= 50) {
    deliveryFee = 0;
  }
  
  this.deliveryInfo.deliveryFee = Math.round(deliveryFee);
  this.deliveryInfo.deliveryDiscount = discount;
  
  return this.deliveryInfo.deliveryFee;
};

export default mongoose.model('Order', orderSchema);



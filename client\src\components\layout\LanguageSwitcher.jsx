import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  LanguageIcon, 
  ChevronDownIcon,
  CheckIcon 
} from '@heroicons/react/24/outline';

const LanguageSwitcher = ({ className = '', showLabel = true, size = 'md' }) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸'
    },
    {
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷'
    }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode);
    setIsOpen(false);
    
    // Store language preference
    localStorage.setItem('preferred-language', languageCode);
    
    // Update document language attribute
    document.documentElement.lang = languageCode;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-sm px-2 py-1';
      case 'lg':
        return 'text-lg px-4 py-3';
      default:
        return 'text-base px-3 py-2';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-6 w-6';
      default:
        return 'h-5 w-5';
    }
  };

  return (
    <div className={`relative inline-block text-left ${className}`}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          inline-flex items-center justify-center rounded-lg border border-gray-300 
          bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 
          focus:ring-orange-500 focus:ring-offset-2 transition-colors
          ${getSizeClasses()}
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <LanguageIcon className={`${getIconSize()} text-gray-500 mr-2`} />
        
        <span className="flex items-center">
          <span className="mr-2 text-lg">{currentLanguage.flag}</span>
          {showLabel && (
            <span className="font-medium text-gray-700">
              {currentLanguage.nativeName}
            </span>
          )}
        </span>
        
        <ChevronDownIcon 
          className={`${getIconSize()} ml-2 text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.15 }}
              className="absolute right-0 z-20 mt-2 w-56 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
            >
              <div className="py-1">
                <div className="px-4 py-2 text-sm text-gray-500 border-b border-gray-100">
                  {t('selectLanguage', 'Select Language')}
                </div>
                
                {languages.map((language) => {
                  const isSelected = language.code === i18n.language;
                  
                  return (
                    <motion.button
                      key={language.code}
                      onClick={() => handleLanguageChange(language.code)}
                      className={`
                        group flex w-full items-center px-4 py-3 text-sm transition-colors
                        ${isSelected 
                          ? 'bg-orange-50 text-orange-700' 
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `}
                      whileHover={{ x: 4 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="mr-3 text-lg">{language.flag}</span>
                      
                      <div className="flex-1 text-left">
                        <div className="font-medium">{language.nativeName}</div>
                        <div className="text-xs text-gray-500">{language.name}</div>
                      </div>
                      
                      {isSelected && (
                        <CheckIcon className="h-4 w-4 text-orange-600" />
                      )}
                    </motion.button>
                  );
                })}
              </div>
              
              {/* Language Info */}
              <div className="border-t border-gray-100 px-4 py-3">
                <p className="text-xs text-gray-500">
                  {t('languageChangeNote', 'Language changes apply to the entire application')}
                </p>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

// Compact version for mobile/small spaces
export const CompactLanguageSwitcher = ({ className = '' }) => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'en', flag: '🇺🇸', name: 'EN' },
    { code: 'fr', flag: '🇫🇷', name: 'FR' }
  ];

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];

  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode);
    setIsOpen(false);
    localStorage.setItem('preferred-language', languageCode);
    document.documentElement.lang = languageCode;
  };

  return (
    <div className={`relative ${className}`}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-2 py-1 rounded-md hover:bg-gray-100 transition-colors"
        whileTap={{ scale: 0.95 }}
      >
        <span className="text-sm">{currentLanguage.flag}</span>
        <span className="text-xs font-medium text-gray-600">{currentLanguage.name}</span>
        <ChevronDownIcon className={`h-3 w-3 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="absolute right-0 z-20 mt-1 w-20 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"
            >
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  className={`
                    flex items-center w-full px-2 py-2 text-xs hover:bg-gray-50 first:rounded-t-md last:rounded-b-md
                    ${language.code === i18n.language ? 'bg-orange-50 text-orange-700' : 'text-gray-700'}
                  `}
                >
                  <span className="mr-1">{language.flag}</span>
                  <span className="font-medium">{language.name}</span>
                </button>
              ))}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSwitcher;

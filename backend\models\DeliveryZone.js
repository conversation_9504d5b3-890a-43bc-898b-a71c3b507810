import mongoose from 'mongoose';

const deliveryZoneSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  // Geographic boundaries (simplified - in production use GeoJSON)
  boundaries: {
    type: [{
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    }],
    default: []
  },
  // Distance from restaurant (in km)
  distanceFromRestaurant: {
    type: Number,
    required: true,
    min: 0
  },
  // Base delivery fee for this zone
  baseFee: {
    type: Number,
    required: true,
    min: 0,
    default: 1000
  },
  // Additional fee per km beyond base distance
  perKmRate: {
    type: Number,
    default: 200,
    min: 0
  },
  // Free delivery threshold for this zone
  freeDeliveryThreshold: {
    type: Number,
    default: 50000,
    min: 0
  },
  // Delivery time estimate (in minutes)
  estimatedDeliveryTime: {
    type: Number,
    default: 30,
    min: 10
  },
  // Whether delivery is available to this zone
  isActive: {
    type: Boolean,
    default: true
  },
  // Popular locations within this zone
  popularLocations: [{
    name: { type: String, required: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    landmarks: [String]
  }],
  // Special pricing rules
  specialRules: [{
    name: String,
    condition: String, // e.g., 'order_value_above', 'time_based', 'day_of_week'
    value: mongoose.Schema.Types.Mixed,
    discount: Number, // percentage discount
    isActive: { type: Boolean, default: true }
  }]
}, {
  timestamps: true
});

// Index for geospatial queries (if using GeoJSON in future)
deliveryZoneSchema.index({ boundaries: '2dsphere' });
deliveryZoneSchema.index({ distanceFromRestaurant: 1 });
deliveryZoneSchema.index({ isActive: 1 });

// Method to calculate delivery fee for this zone
deliveryZoneSchema.methods.calculateDeliveryFee = function(orderValue, distance = null) {
  if (!this.isActive) {
    return {
      fee: 0,
      available: false,
      message: 'Delivery not available to this zone'
    };
  }

  const actualDistance = distance || this.distanceFromRestaurant;
  let fee = this.baseFee;
  
  // Add distance-based fee
  if (actualDistance > 5) {
    fee += (actualDistance - 5) * this.perKmRate;
  }

  // Apply order value discounts
  let discount = 0;
  let discountReason = '';

  // Standard discount tiers
  if (orderValue >= 10000) {
    discount = 10;
    discountReason = '10% discount for orders above 10,000 XAF';
  }
  if (orderValue >= 20000) {
    discount = 25;
    discountReason = '25% discount for orders above 20,000 XAF';
  }
  if (orderValue >= 50000) {
    discount = 50;
    discountReason = '50% discount for orders above 50,000 XAF';
  }

  // Free delivery for high-value orders
  if (orderValue >= this.freeDeliveryThreshold) {
    fee = 0;
    discount = 100;
    discountReason = `Free delivery for orders above ${this.freeDeliveryThreshold.toLocaleString()} XAF`;
  }

  // Apply special rules
  for (const rule of this.specialRules) {
    if (!rule.isActive) continue;
    
    if (rule.condition === 'order_value_above' && orderValue >= rule.value) {
      if (rule.discount > discount) {
        discount = rule.discount;
        discountReason = rule.name;
      }
    }
  }

  const originalFee = fee;
  if (discount > 0 && discount < 100) {
    fee = Math.round(fee * (1 - discount / 100));
  }

  return {
    fee: Math.max(0, fee),
    originalFee,
    discount,
    discountReason,
    distance: actualDistance,
    estimatedTime: this.estimatedDeliveryTime,
    available: true,
    breakdown: {
      baseFee: this.baseFee,
      distanceFee: actualDistance > 5 ? (actualDistance - 5) * this.perKmRate : 0,
      discountAmount: originalFee - fee
    }
  };
};

// Static method to find zone by location name or coordinates
deliveryZoneSchema.statics.findZoneByLocation = async function(location) {
  // Simple text-based matching for now
  // In production, use geospatial queries
  const zones = await this.find({ isActive: true });
  
  for (const zone of zones) {
    // Check popular locations
    for (const popularLocation of zone.popularLocations) {
      if (popularLocation.name.toLowerCase().includes(location.toLowerCase()) ||
          location.toLowerCase().includes(popularLocation.name.toLowerCase())) {
        return zone;
      }
      
      // Check landmarks
      for (const landmark of popularLocation.landmarks) {
        if (landmark.toLowerCase().includes(location.toLowerCase()) ||
            location.toLowerCase().includes(landmark.toLowerCase())) {
          return zone;
        }
      }
    }
    
    // Check zone name
    if (zone.name.toLowerCase().includes(location.toLowerCase()) ||
        location.toLowerCase().includes(zone.name.toLowerCase())) {
      return zone;
    }
  }
  
  // Default to closest zone if no match found
  return zones.sort((a, b) => a.distanceFromRestaurant - b.distanceFromRestaurant)[0];
};

const DeliveryZone = mongoose.model('DeliveryZone', deliveryZoneSchema);

export default DeliveryZone;

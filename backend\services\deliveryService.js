import DeliveryZone from '../models/DeliveryZone.js';

class DeliveryService {
  constructor() {
    this.defaultBaseFee = 1000;
    this.defaultPerKmRate = 200;
    this.freeDeliveryThreshold = 50000;
  }

  /**
   * Calculate delivery fee based on location and order value
   * @param {string} location - Delivery location/address
   * @param {number} orderValue - Total order value in XAF
   * @param {object} options - Additional options
   * @returns {object} Delivery calculation result
   */
  async calculateDeliveryFee(location, orderValue, options = {}) {
    try {
      // Find the appropriate delivery zone
      const zone = await DeliveryZone.findZoneByLocation(location);
      
      if (zone) {
        return zone.calculateDeliveryFee(orderValue, options.distance);
      }

      // Fallback calculation if no zone found
      return this.fallbackCalculation(location, orderValue, options);
    } catch (error) {
      console.error('Error calculating delivery fee:', error);
      return this.fallbackCalculation(location, orderValue, options);
    }
  }

  /**
   * Fallback calculation when no zone is found
   */
  fallbackCalculation(location, orderValue, options = {}) {
    const distance = options.distance || this.estimateDistance(location);
    let fee = this.defaultBaseFee;
    
    // Add distance-based fee
    if (distance > 5) {
      fee += (distance - 5) * this.defaultPerKmRate;
    }

    // Apply standard discounts
    let discount = 0;
    let discountReason = '';

    if (orderValue >= 10000) {
      discount = 10;
      discountReason = '10% discount for orders above 10,000 XAF';
    }
    if (orderValue >= 20000) {
      discount = 25;
      discountReason = '25% discount for orders above 20,000 XAF';
    }
    if (orderValue >= 50000) {
      discount = 50;
      discountReason = '50% discount for orders above 50,000 XAF';
    }

    // Free delivery for very high-value orders
    if (orderValue >= this.freeDeliveryThreshold) {
      fee = 0;
      discount = 100;
      discountReason = `Free delivery for orders above ${this.freeDeliveryThreshold.toLocaleString()} XAF`;
    }

    const originalFee = fee;
    if (discount > 0 && discount < 100) {
      fee = Math.round(fee * (1 - discount / 100));
    }

    return {
      fee: Math.max(0, fee),
      originalFee,
      discount,
      discountReason,
      distance,
      estimatedTime: this.estimateDeliveryTime(distance),
      available: true,
      zone: 'Default Zone',
      breakdown: {
        baseFee: this.defaultBaseFee,
        distanceFee: distance > 5 ? (distance - 5) * this.defaultPerKmRate : 0,
        discountAmount: originalFee - fee
      }
    };
  }

  /**
   * Estimate distance based on location (placeholder)
   * In production, integrate with Google Maps Distance Matrix API
   */
  estimateDistance(location) {
    const locationLower = location.toLowerCase();
    
    // Simple distance estimation based on known areas
    const distanceMap = {
      'douala': 5,
      'akwa': 3,
      'bonanjo': 4,
      'deido': 6,
      'new bell': 7,
      'makepe': 8,
      'logpom': 10,
      'bonaberi': 12,
      'pk': 15,
      'yaounde': 250, // Different city
      'bafoussam': 300,
      'bamenda': 400
    };

    for (const [area, distance] of Object.entries(distanceMap)) {
      if (locationLower.includes(area)) {
        return distance;
      }
    }

    // Default distance for unknown locations
    return Math.floor(Math.random() * 15) + 5;
  }

  /**
   * Estimate delivery time based on distance
   */
  estimateDeliveryTime(distance) {
    const baseTime = 20; // 20 minutes base time
    const timePerKm = 3; // 3 minutes per km
    
    return Math.round(baseTime + (distance * timePerKm));
  }

  /**
   * Get all available delivery zones
   */
  async getDeliveryZones() {
    try {
      return await DeliveryZone.find({ isActive: true })
        .select('name description distanceFromRestaurant baseFee estimatedDeliveryTime popularLocations')
        .sort({ distanceFromRestaurant: 1 });
    } catch (error) {
      console.error('Error fetching delivery zones:', error);
      return [];
    }
  }

  /**
   * Check if delivery is available to a location
   */
  async isDeliveryAvailable(location) {
    try {
      const zone = await DeliveryZone.findZoneByLocation(location);
      return zone ? zone.isActive : true; // Default to available
    } catch (error) {
      console.error('Error checking delivery availability:', error);
      return true; // Default to available
    }
  }

  /**
   * Get delivery options for a location
   */
  async getDeliveryOptions(location, orderValue) {
    try {
      const calculation = await this.calculateDeliveryFee(location, orderValue);
      const zones = await this.getDeliveryZones();
      
      return {
        calculation,
        availableZones: zones,
        recommendations: this.getRecommendations(orderValue, calculation)
      };
    } catch (error) {
      console.error('Error getting delivery options:', error);
      return {
        calculation: this.fallbackCalculation(location, orderValue),
        availableZones: [],
        recommendations: []
      };
    }
  }

  /**
   * Get recommendations to reduce delivery cost
   */
  getRecommendations(orderValue, calculation) {
    const recommendations = [];
    
    if (calculation.discount < 10 && orderValue < 10000) {
      const needed = 10000 - orderValue;
      recommendations.push({
        type: 'discount',
        message: `Add ${needed.toLocaleString()} XAF more to get 10% delivery discount`,
        savings: Math.round(calculation.originalFee * 0.1)
      });
    }
    
    if (calculation.discount < 25 && orderValue < 20000) {
      const needed = 20000 - orderValue;
      recommendations.push({
        type: 'discount',
        message: `Add ${needed.toLocaleString()} XAF more to get 25% delivery discount`,
        savings: Math.round(calculation.originalFee * 0.25)
      });
    }
    
    if (calculation.discount < 50 && orderValue < 50000) {
      const needed = 50000 - orderValue;
      recommendations.push({
        type: 'discount',
        message: `Add ${needed.toLocaleString()} XAF more to get 50% delivery discount`,
        savings: Math.round(calculation.originalFee * 0.5)
      });
    }
    
    if (calculation.fee > 0 && orderValue < this.freeDeliveryThreshold) {
      const needed = this.freeDeliveryThreshold - orderValue;
      recommendations.push({
        type: 'free_delivery',
        message: `Add ${needed.toLocaleString()} XAF more for FREE delivery`,
        savings: calculation.fee
      });
    }
    
    return recommendations;
  }
}

export default new DeliveryService();

import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CameraIcon, 
  UserIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ProfilePicture = ({ 
  user, 
  onImageUpdate, 
  size = 'lg',
  editable = true,
  showUploadButton = true 
}) => {
  const { t } = useTranslation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef(null);

  // Get user initials
  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-12 h-12 text-sm';
      case 'md':
        return 'w-16 h-16 text-lg';
      case 'lg':
        return 'w-24 h-24 text-2xl';
      case 'xl':
        return 'w-32 h-32 text-3xl';
      default:
        return 'w-24 h-24 text-2xl';
    }
  };

  // Get background color based on name
  const getBackgroundColor = (name) => {
    if (!name) return 'bg-gray-500';
    
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500',
      'bg-orange-500', 'bg-cyan-500'
    ];
    
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error(t('invalidFileType', 'Please select an image file'));
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error(t('fileTooLarge', 'File size must be less than 5MB'));
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target.result);
      setShowPreview(true);
    };
    reader.readAsDataURL(file);
  };

  // Handle image upload
  const handleImageUpload = async () => {
    if (!previewImage) return;

    setIsUploading(true);
    try {
      // Convert data URL to blob
      const response = await fetch(previewImage);
      const blob = await response.blob();
      
      // Create form data
      const formData = new FormData();
      formData.append('profilePicture', blob, 'profile.jpg');
      
      // Simulate upload (replace with actual API call)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update user profile
      if (onImageUpdate) {
        onImageUpdate(previewImage);
      }
      
      toast.success(t('profilePictureUpdated', 'Profile picture updated successfully'));
      setShowPreview(false);
      setPreviewImage(null);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(t('uploadFailed', 'Failed to upload image'));
    } finally {
      setIsUploading(false);
    }
  };

  // Handle remove image
  const handleRemoveImage = () => {
    if (onImageUpdate) {
      onImageUpdate(null);
    }
    setShowPreview(false);
    setPreviewImage(null);
    toast.success(t('profilePictureRemoved', 'Profile picture removed'));
  };

  const sizeClasses = getSizeClasses();
  const initials = getInitials(user?.name);
  const backgroundColor = getBackgroundColor(user?.name);

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Profile Picture Display */}
      <div className="relative">
        <div className={`
          ${sizeClasses} rounded-full overflow-hidden border-4 border-white shadow-lg
          flex items-center justify-center font-bold text-white
          ${user?.profilePicture ? '' : backgroundColor}
        `}>
          {user?.profilePicture ? (
            <img
              src={user.profilePicture}
              alt={user.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <span>{initials}</span>
          )}
        </div>

        {/* Upload Button Overlay */}
        {editable && (
          <motion.button
            onClick={() => fileInputRef.current?.click()}
            className="absolute bottom-0 right-0 bg-orange-600 text-white p-2 rounded-full shadow-lg hover:bg-orange-700 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <CameraIcon className="h-4 w-4" />
          </motion.button>
        )}
      </div>

      {/* Upload Button */}
      {editable && showUploadButton && (
        <button
          onClick={() => fileInputRef.current?.click()}
          className="text-sm text-orange-600 hover:text-orange-700 font-medium transition-colors"
        >
          {user?.profilePicture ? t('changePhoto', 'Change Photo') : t('addPhoto', 'Add Photo')}
        </button>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Preview Modal */}
      <AnimatePresence>
        {showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-lg p-6 max-w-md w-full"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('previewProfilePicture', 'Preview Profile Picture')}
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Preview Image */}
              <div className="flex justify-center mb-6">
                <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200">
                  <img
                    src={previewImage}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <motion.button
                  onClick={handleImageUpload}
                  disabled={isUploading}
                  className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isUploading ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      {t('uploading', 'Uploading...')}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {t('savePhoto', 'Save Photo')}
                    </div>
                  )}
                </motion.button>
                
                <button
                  onClick={() => setShowPreview(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  {t('cancel', 'Cancel')}
                </button>
              </div>

              {/* Remove Option */}
              {user?.profilePicture && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={handleRemoveImage}
                    className="w-full text-red-600 hover:text-red-700 text-sm font-medium transition-colors"
                  >
                    {t('removeCurrentPhoto', 'Remove Current Photo')}
                  </button>
                </div>
              )}
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Compact version for headers/navigation
export const CompactProfilePicture = ({ user, size = 'sm', onClick }) => {
  const sizeClasses = size === 'sm' ? 'w-8 h-8 text-xs' : 'w-10 h-10 text-sm';
  const initials = user?.name ? user.name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2) : 'U';
  
  const colors = [
    'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
  ];
  const backgroundColor = user?.name ? colors[user.name.charCodeAt(0) % colors.length] : 'bg-gray-500';

  return (
    <button
      onClick={onClick}
      className={`
        ${sizeClasses} rounded-full overflow-hidden border-2 border-white shadow-sm
        flex items-center justify-center font-bold text-white transition-transform hover:scale-105
        ${user?.profilePicture ? '' : backgroundColor}
      `}
    >
      {user?.profilePicture ? (
        <img
          src={user.profilePicture}
          alt={user.name}
          className="w-full h-full object-cover"
        />
      ) : (
        <span>{initials}</span>
      )}
    </button>
  );
};

export default ProfilePicture;

{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "vite": "^7.0.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "framer-motion": "^12.23.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "socket.io-client": "^4.8.1", "swiper": "^11.2.10", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}}
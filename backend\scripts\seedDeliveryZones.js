import mongoose from 'mongoose';
import dotenv from 'dotenv';
import DeliveryZone from '../models/DeliveryZone.js';

// Load environment variables
dotenv.config();

const deliveryZones = [
  {
    name: 'Akwa',
    description: 'Central business district of Douala',
    distanceFromRestaurant: 3,
    baseFee: 800,
    perKmRate: 150,
    freeDeliveryThreshold: 40000,
    estimatedDeliveryTime: 20,
    popularLocations: [
      {
        name: 'Akwa Palace',
        coordinates: { lat: 4.0511, lng: 9.7679 },
        landmarks: ['Akwa Palace Hotel', 'Cathedral', 'Central Post Office']
      },
      {
        name: 'Rue Joss',
        coordinates: { lat: 4.0501, lng: 9.7689 },
        landmarks: ['Rue Joss', 'Commercial Avenue', 'Banks']
      }
    ],
    specialRules: [
      {
        name: 'Business Hours Discount',
        condition: 'time_based',
        value: { start: '09:00', end: '17:00' },
        discount: 15,
        isActive: true
      }
    ]
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    description: 'Administrative and business quarter',
    distanceFromRestaurant: 4,
    baseFee: 900,
    perKmRate: 180,
    freeDeliveryThreshold: 45000,
    estimatedDeliveryTime: 25,
    popularLocations: [
      {
        name: 'Bonanjo Center',
        coordinates: { lat: 4.0461, lng: 9.7719 },
        landmarks: ['Government Buildings', 'Courts', 'Ministry Buildings']
      },
      {
        name: 'Port Area',
        coordinates: { lat: 4.0441, lng: 9.7739 },
        landmarks: ['Douala Port', 'Customs', 'Maritime Station']
      }
    ]
  },
  {
    name: 'Deido',
    description: 'Residential and commercial area',
    distanceFromRestaurant: 6,
    baseFee: 1000,
    perKmRate: 200,
    freeDeliveryThreshold: 50000,
    estimatedDeliveryTime: 30,
    popularLocations: [
      {
        name: 'Deido Market',
        coordinates: { lat: 4.0611, lng: 9.7579 },
        landmarks: ['Deido Market', 'Schools', 'Residential Areas']
      },
      {
        name: 'New Deido',
        coordinates: { lat: 4.0631, lng: 9.7559 },
        landmarks: ['New Deido', 'Shopping Centers', 'Hospitals']
      }
    ]
  },
  {
    name: 'New Bell',
    description: 'Popular residential and commercial district',
    distanceFromRestaurant: 7,
    baseFee: 1100,
    perKmRate: 220,
    freeDeliveryThreshold: 55000,
    estimatedDeliveryTime: 35,
    popularLocations: [
      {
        name: 'New Bell Market',
        coordinates: { lat: 4.0711, lng: 9.7479 },
        landmarks: ['New Bell Market', 'Taxi Station', 'Commercial Street']
      },
      {
        name: 'New Bell Residential',
        coordinates: { lat: 4.0731, lng: 9.7459 },
        landmarks: ['Residential Quarters', 'Schools', 'Churches']
      }
    ]
  },
  {
    name: 'Makepe',
    description: 'Suburban residential area',
    distanceFromRestaurant: 8,
    baseFee: 1200,
    perKmRate: 250,
    freeDeliveryThreshold: 60000,
    estimatedDeliveryTime: 40,
    popularLocations: [
      {
        name: 'Makepe Missoke',
        coordinates: { lat: 4.0811, lng: 9.7379 },
        landmarks: ['Makepe Missoke', 'University Campus', 'Student Areas']
      },
      {
        name: 'Makepe Petit Pays',
        coordinates: { lat: 4.0831, lng: 9.7359 },
        landmarks: ['Petit Pays', 'Residential Estates', 'Shopping Areas']
      }
    ]
  },
  {
    name: 'Logpom',
    description: 'Industrial and residential zone',
    distanceFromRestaurant: 10,
    baseFee: 1400,
    perKmRate: 280,
    freeDeliveryThreshold: 65000,
    estimatedDeliveryTime: 45,
    popularLocations: [
      {
        name: 'Logpom Industrial',
        coordinates: { lat: 4.0911, lng: 9.7279 },
        landmarks: ['Industrial Zone', 'Factories', 'Warehouses']
      },
      {
        name: 'Logpom Residential',
        coordinates: { lat: 4.0931, lng: 9.7259 },
        landmarks: ['Residential Areas', 'Local Markets', 'Schools']
      }
    ]
  },
  {
    name: 'Bonaberi',
    description: 'Across the Wouri River',
    distanceFromRestaurant: 12,
    baseFee: 1600,
    perKmRate: 300,
    freeDeliveryThreshold: 70000,
    estimatedDeliveryTime: 50,
    popularLocations: [
      {
        name: 'Bonaberi Center',
        coordinates: { lat: 4.0311, lng: 9.6879 },
        landmarks: ['Bonaberi Bridge', 'Main Market', 'Administrative Center']
      },
      {
        name: 'Bonaberi Industrial',
        coordinates: { lat: 4.0291, lng: 9.6859 },
        landmarks: ['Industrial Area', 'Port Extension', 'Logistics Centers']
      }
    ],
    specialRules: [
      {
        name: 'Bridge Crossing Fee Waiver',
        condition: 'order_value_above',
        value: 30000,
        discount: 20,
        isActive: true
      }
    ]
  },
  {
    name: 'PK Areas',
    description: 'Suburban areas along major roads',
    distanceFromRestaurant: 15,
    baseFee: 2000,
    perKmRate: 350,
    freeDeliveryThreshold: 80000,
    estimatedDeliveryTime: 60,
    popularLocations: [
      {
        name: 'PK 8',
        coordinates: { lat: 4.1011, lng: 9.6979 },
        landmarks: ['PK 8', 'Residential Estates', 'Shopping Centers']
      },
      {
        name: 'PK 10',
        coordinates: { lat: 4.1111, lng: 9.6879 },
        landmarks: ['PK 10', 'University Areas', 'Student Hostels']
      },
      {
        name: 'PK 12',
        coordinates: { lat: 4.1211, lng: 9.6779 },
        landmarks: ['PK 12', 'Suburban Areas', 'Local Markets']
      }
    ]
  }
];

async function seedDeliveryZones() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB');

    // Clear existing delivery zones
    await DeliveryZone.deleteMany({});
    console.log('🗑️  Cleared existing delivery zones');

    // Insert new delivery zones
    const insertedZones = await DeliveryZone.insertMany(deliveryZones);
    console.log(`✅ Inserted ${insertedZones.length} delivery zones`);

    // Display inserted zones
    insertedZones.forEach(zone => {
      console.log(`   - ${zone.name}: ${zone.baseFee} XAF base fee, ${zone.distanceFromRestaurant}km distance`);
    });

    console.log('🎉 Delivery zones seeded successfully!');
    
  } catch (error) {
    console.error('❌ Error seeding delivery zones:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the seed function
seedDeliveryZones();
